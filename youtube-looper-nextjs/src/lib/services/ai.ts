// Firebase AI Logic service for generating video recommendations

import { getAI, getGenerativeModel, GoogleAIBackend } from 'firebase/ai'
import { getFirebaseApp } from '@/lib/firebase/config'
import { VideoSearchResult } from '@/lib/types/video'

export interface MagicQueueRequest {
  prompt: string
  count?: number
}

export interface MagicQueueResponse {
  videos: VideoSearchResult[]
  explanation?: string
}

export class AIService {
  private getAI() {
    const app = getFirebaseApp()
    if (!app) {
      throw new Error('Firebase not initialized. AI service will not work.')
    }
    
    try {
      return getAI(app, { backend: new GoogleAIBackend() })
    } catch (error) {
      console.error('❌ Error initializing Firebase AI:', error)
      throw new Error('Firebase AI not configured properly. Please check your Firebase AI Logic setup.')
    }
  }

  private getModel() {
    const ai = this.getAI()
    return getGenerativeModel(ai, { model: 'gemini-2.5-flash' })
  }

  /**
   * Generate video recommendations based on user prompt
   */
  async generateVideoRecommendations(request: MagicQueueRequest): Promise<MagicQueueResponse> {
    const { prompt, count = 10 } = request

    try {
      console.log('🪄 Generating magic queue for prompt:', prompt)
      
      const model = this.getModel()
      
      const systemPrompt = `You are a YouTube video recommendation expert. Based on the user's request, suggest ${count} specific YouTube videos that would be perfect for their needs.

IMPORTANT: You must respond with a valid JSON object in this exact format:
{
  "videos": [
    {
      "title": "Exact video title",
      "channel": "Channel name",
      "description": "Brief description of what this video covers",
      "searchQuery": "Specific search terms to find this video on YouTube"
    }
  ],
  "explanation": "Brief explanation of why these videos were chosen"
}

Guidelines:
- Suggest real, popular, high-quality videos that likely exist on YouTube
- Include a mix of content types (tutorials, documentaries, talks, etc.) when appropriate
- Make sure video titles and channel names are realistic and searchable
- Each searchQuery should be specific enough to find the actual video
- Focus on educational, entertaining, or valuable content
- Avoid suggesting copyrighted music videos unless specifically requested

User request: "${prompt}"`

      const result = await model.generateContent(systemPrompt)
      const responseText = result.response.text()
      
      console.log('🤖 AI Response:', responseText)

      // Parse the JSON response
      let parsedResponse
      try {
        // Extract JSON from response if it's wrapped in markdown code blocks
        const jsonMatch = responseText.match(/```(?:json)?\s*([\s\S]*?)\s*```/)
        const jsonText = jsonMatch ? jsonMatch[1] : responseText
        parsedResponse = JSON.parse(jsonText)
      } catch (parseError) {
        console.error('❌ Failed to parse AI response as JSON:', parseError)
        throw new Error('AI response was not in the expected format')
      }

      // Validate the response structure
      if (!parsedResponse.videos || !Array.isArray(parsedResponse.videos)) {
        throw new Error('AI response missing videos array')
      }

      // Transform AI response to VideoSearchResult format
      const videos: VideoSearchResult[] = parsedResponse.videos.map((video: any, index: number) => ({
        id: `ai-${Date.now()}-${index}`, // Temporary ID for AI-generated suggestions
        title: video.title || 'Untitled Video',
        description: video.description || '',
        thumbnail: {
          url: 'https://img.youtube.com/vi/placeholder/mqdefault.jpg', // Placeholder thumbnail
          width: 320,
          height: 180
        },
        channel: {
          title: video.channel || 'Unknown Channel',
          id: 'unknown'
        },
        duration: 'PT0S', // Will be filled when actual video is found
        publishedAt: new Date().toISOString(),
        viewCount: 0,
        // Add custom properties for AI recommendations
        searchQuery: video.searchQuery || video.title // Store the search query for later use
      } as VideoSearchResult & { searchQuery: string }))

      console.log(`✅ Generated ${videos.length} video recommendations`)

      return {
        videos,
        explanation: parsedResponse.explanation || 'AI-generated video recommendations based on your request.'
      }

    } catch (error) {
      console.error('❌ Error generating video recommendations:', error)
      
      if (error instanceof Error) {
        throw error
      }
      
      throw new Error('Failed to generate video recommendations. Please try again.')
    }
  }

  /**
   * Check if Firebase AI Logic is properly configured
   */
  async checkAIConfiguration(): Promise<boolean> {
    try {
      const model = this.getModel()
      // Try a simple test request
      await model.generateContent('Test')
      return true
    } catch (error) {
      console.error('❌ AI configuration check failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const aiService = new AIService()
