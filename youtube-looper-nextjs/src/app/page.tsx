'use client'

import { MainLayout } from '@/components/layout/MainLayout'
import { VideoPlayer } from '@/components/video-player/VideoPlayer'
import { TimeframeDisplay } from '@/components/video-player/TimeframeDisplay'
import { CurrentQueue } from '@/components/queue/CurrentQueue'
import { SearchView } from '@/components/search/SearchView'
import { PersonalQueuesView } from '@/components/personal-queues/PersonalQueuesView'
import { PublicQueuesView } from '@/components/public-queues/PublicQueuesView'
import { useNavigation } from '@/hooks/useNavigation'
import { useHeaderMinimize } from '@/components/layout/Header'
import { useQueue } from '@/hooks/useQueue'
import { firebaseService } from '@/lib/services/firebase'
import { useFirebase } from '@/components/providers/FirebaseProvider'
import { transformQueueData } from '@/lib/utils/queue-transform'
import { useEffect, useState, useRef } from 'react'

export default function HomePage() {
  const { activeView } = useNavigation()
  const { isMinimized } = useHeaderMinimize()
  const { loadQueue } = useQueue()
  const { isInitialized, db } = useFirebase()
  const [isLoadingSharedQueue, setIsLoadingSharedQueue] = useState(false)

  useEffect(() => {
    // Initialize YouTube API when component mounts
    const initYouTubeAPI = () => {
      if (typeof window !== 'undefined' && !window.YT) {
        const tag = document.createElement('script')
        tag.src = 'https://www.youtube.com/iframe_api'
        const firstScriptTag = document.getElementsByTagName('script')[0]
        firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag)
      }
    }

    initYouTubeAPI()
  }, [])

  // Separate effect for checking URL queue that waits for Firebase initialization
  useEffect(() => {
    // Only proceed if Firebase is initialized
    if (!isInitialized) {
      return
    }

    // Check for queue ID in URL parameters
    const checkURLForQueue = async () => {
      const urlParams = new URLSearchParams(window.location.search)
      const queueId = urlParams.get('q')

      if (queueId) {
        console.log('🔗 Found queue ID in URL:', queueId)

        // Check if Firebase database is available
        if (!db) {
          console.warn('⚠️ Firebase not configured, cannot load shared queue')
          return
        }

        setIsLoadingSharedQueue(true)

        try {
          const publicQueue = await firebaseService.getPublicQueue(queueId)
          if (publicQueue) {
            // Transform queue data without auto-play - let user decide when to play
            const transformedQueueData = transformQueueData(publicQueue.queueData, false)
            loadQueue(transformedQueueData)
            console.log('✅ Queue loaded from URL:', publicQueue.metadata.title)

            // Clean up URL without refreshing
            window.history.replaceState({}, document.title, window.location.pathname)
          } else {
            console.warn('Queue not found:', queueId)
          }
        } catch (error) {
          console.error('Failed to load queue from URL:', error)
        } finally {
          setIsLoadingSharedQueue(false)
        }
      }
    }

    checkURLForQueue()
  }, [isInitialized, db, loadQueue])

  const renderActiveView = () => {
    switch (activeView) {
      case 'search':
        return <SearchView />
      case 'personal':
        return <PersonalQueuesView />
      case 'public':
        return <PublicQueuesView />
      default:
        return <SearchView />
    }
  }

  return (
    <>
      {/* Simple centered loader */}
      {isLoadingSharedQueue && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center pointer-events-none">
          <div className="w-8 h-8 border-2 border-primary-400/30 border-t-primary-400 rounded-full animate-spin"></div>
        </div>
      )}

      <MainLayout>
        {/* Media Control Center - Video Player and Current Queue */}
      <div
        className={`media-control-center ${
          isMinimized
            ? 'opacity-0 max-h-0 mb-0 overflow-hidden pointer-events-none'
            : 'opacity-100 mb-8'
        }`}
      >
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <VideoPlayer />
          <CurrentQueue />
        </div>

        {/* Timeframe Display - Full Width Below Player and Queue */}
        <TimeframeDisplay />
      </div>

      {/* Main Content Views */}
      <div className="content-area">
        {renderActiveView()}
      </div>
    </MainLayout>
    </>
  )
}
