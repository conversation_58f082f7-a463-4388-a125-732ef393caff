# Magic Queue Setup Guide

The Magic Queue feature allows users to create video queues using AI-powered recommendations. Instead of manually searching for videos, users can describe what they want to watch, and AI will generate a curated list of video recommendations.

## Prerequisites

- Firebase project with Firebase AI Logic configured
- Firebase SDK v12.0.0 or higher (automatically installed)

## Firebase AI Logic Setup

### Step 1: Enable Firebase AI Logic in Firebase Console

1. Go to the [Firebase Console](https://console.firebase.google.com)
2. Select your project
3. Navigate to **Firebase AI Logic** section
4. Click **Get Started** to begin setup

### Step 2: Choose Your AI Provider

You have two options:

#### Option A: Gemini Developer API (Recommended for beginners)
- **Cost**: Free tier available with generous limits
- **Setup**: Automatic API key management
- **Billing**: No billing required for free tier

1. Select **Gemini Developer API** in the console
2. The console will automatically:
   - Enable required APIs
   - Create and manage API keys
   - Configure your project

#### Option B: Vertex AI Gemini API (For production/advanced users)
- **Cost**: Pay-per-use pricing
- **Setup**: Requires billing account
- **Features**: Advanced features and higher limits

1. Select **Vertex AI Gemini API** in the console
2. Set up billing (requires Blaze plan)
3. Enable required APIs
4. Configure location settings

### Step 3: Verify Setup

1. After completing the setup in Firebase Console
2. Refresh your application
3. Navigate to the **Magic Queue** tab
4. If setup is correct, you'll see the prompt input interface
5. If setup is incomplete, you'll see setup instructions with a link to Firebase Console

## How to Use Magic Queue

### 1. Access Magic Queue
- Click the **Magic Queue** icon (magic wand) in the navigation menu
- It's the second item between "Create Queue" and "My Queues"

### 2. Enter Your Prompt
- Type a description of what you want to watch
- Examples:
  - "Educational videos about Programming"
  - "Teach me something new"
  - "Relaxing nature documentaries"
  - "Beginner guitar tutorials"

### 3. Generate Recommendations
- Click **Generate Magic Queue**
- AI will analyze your prompt and generate 10 video recommendations
- Each recommendation includes:
  - Video title
  - Channel name
  - Description
  - Search query that will be used to find the actual video

### 4. Add Videos to Queue
- **Add Individual Videos**: Click the + button on any recommendation
- **Add All Videos**: Click the "Add All" button to add all recommendations
- Videos are added to your draft queue

### 5. Customize and Save
- Review your draft queue
- Add/remove videos as needed
- Add custom timeframes if desired
- Configure loop settings
- Save your queue with a title and description

## Features

### AI-Powered Recommendations
- Natural language processing understands your intent
- Generates diverse, high-quality video suggestions
- Provides search queries for finding actual videos on YouTube

### Seamless Integration
- Works with existing queue creation flow
- Reuses all existing components (Draft Queue, Queue Creation Form)
- Maintains all current functionality

### User-Friendly Interface
- Example prompts for inspiration
- Loading states and error handling
- Clear feedback and instructions

### Flexible Workflow
- AI recommendations are suggestions, not requirements
- Full control over final queue composition
- Can mix AI recommendations with manual searches

## Troubleshooting

### "Firebase AI Logic Not Configured" Error
- **Cause**: Firebase AI Logic is not properly set up in your project
- **Solution**: Follow the setup steps above in Firebase Console

### "AI configuration check failed" Error
- **Cause**: API keys or permissions are not properly configured
- **Solution**: 
  1. Check Firebase Console AI Logic settings
  2. Ensure APIs are enabled
  3. Verify billing setup (for Vertex AI)
  4. Try refreshing the page

### "Failed to generate video recommendations" Error
- **Cause**: Temporary AI service issue or invalid prompt
- **Solution**:
  1. Try a different, more specific prompt
  2. Check your internet connection
  3. Wait a moment and try again

### Empty or Poor Recommendations
- **Cause**: Vague or unclear prompt
- **Solution**: 
  1. Be more specific in your request
  2. Include context (e.g., "beginner", "advanced", "tutorial")
  3. Specify the type of content you want

## Best Practices

### Writing Effective Prompts
- **Be Specific**: "JavaScript tutorials for beginners" vs "programming videos"
- **Include Context**: Mention your skill level, interests, or goals
- **Specify Format**: "documentaries", "tutorials", "talks", etc.
- **Add Constraints**: "under 20 minutes", "recent videos", etc.

### Examples of Good Prompts
- "Advanced React.js tutorials covering hooks and state management"
- "Relaxing 4K nature documentaries about forests and wildlife"
- "Beginner-friendly cooking videos for healthy meal prep"
- "TED talks about artificial intelligence and machine learning"
- "Guitar fingerpicking tutorials for intermediate players"

## Technical Details

### AI Model
- Uses **Gemini 2.5 Flash** model for fast, high-quality recommendations
- Optimized for understanding video content requests
- Generates structured responses with video metadata

### Data Flow
1. User prompt → Firebase AI Logic
2. AI generates video recommendations with search queries
3. Recommendations displayed in UI
4. User selects videos to add to draft queue
5. Standard queue creation flow continues

### Privacy & Data
- Prompts are sent to Google's AI services for processing
- No personal data is stored beyond what's needed for the request
- Follow Google's AI usage policies and terms of service

## Support

If you encounter issues:
1. Check the browser console for detailed error messages
2. Verify Firebase AI Logic setup in Firebase Console
3. Ensure you have the latest version of the application
4. Try different prompts to isolate the issue

For additional help, refer to the [Firebase AI Logic documentation](https://firebase.google.com/docs/ai-logic).
